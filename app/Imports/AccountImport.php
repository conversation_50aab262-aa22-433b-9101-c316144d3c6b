<?php

namespace App\Imports;

use App\Http\Constants;
use App\Http\Helpers;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\Config;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Nette\Utils\DateTime;

class AccountImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    private $logicErrors;
    private $count;
    private $totalCount;
    private $mapProvinces;
    private $mapDistricts;
    private $defaultextendexpired;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $provinces = Helpers::getProvinces();
        $districts = Helpers::getDistricts();

        foreach ($provinces as $key => $province){
            $this->mapProvinces[$province['name']] = $province;
            $this->mapProvinces[$key] = $province;
        }

        foreach ($districts as $key => $district){
            $this->mapDistricts[$district['name']] = $district;
            $this->mapDistricts[$key] = $district;
        }

        $this->logicErrors = null;
        $this->count = 0;
        $config = Config::query()->first();
        $this->defaultextendexpired = $config->defaultextendexpired;
    }

    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        DB::beginTransaction();

        $this->totalCount = count($collection);
        if($this->defaultextendexpired != null){
            $dateExpired = now()->addDays($this->defaultextendexpired);
        } else {
            $dateExpired = null;
        }

        foreach ($collection as $row) {
            if($row == null){
                continue;
            }

            // init value
            $values = array_values($row->toArray());
            $name = $values[0];
            $nameOrg = $values[1];
            $phoneNumber = $values[2];

            // check phone number
            $exists = Account::withTrashed()->where('phone_number', $phoneNumber)->first();
            if($exists != null){
                if($exists->deleted_at !== null) {
                    // Force delete the soft-deleted account
                    $exists->forceDelete();
                } else {
                    $this->count++;
                    $this->logicErrors = "Trùng số điện thoại ở dòng " . $this->count++;
                    DB::rollBack();
                    return;
                }
            }

            $email = $values[3];

            // check email
            $exists = Account::withTrashed()->where('email', $email)->first();
            if($exists != null){
                if($exists->deleted_at !== null) {
                    // Force delete the soft-deleted account
                    $exists->forceDelete();
                } else {
                    $this->count++;
                    $this->logicErrors = "Trùng email ở dòng " . $this->count;
                    DB::rollBack();
                    return;
                }
            }

            $provinceName = $values[4];
            $districtName = $values[5];
            $class = $values[6];
            $status = $values[7];
            $isMamnon = $values[8];
            $isTieuhoc = $values[9];

            // check status
            // Only allow active and locked status (pending status is removed)
            if(!in_array($status, [Account::STATUS_ACTIVE, Account::STATUS_LOCK])){
                $this->count++;
                $this->logicErrors = "Mã trạng thái không đúng ở dòng " . $this->count;
                DB::rollBack();
                return;
            }

            // map name to district and province
            $district = null;
            $province = null;
            if(array_key_exists($provinceName, $this->mapProvinces)){
                $province = $this->mapProvinces[$provinceName];
            }

            if(array_key_exists($districtName, $this->mapDistricts)){
                $district = $this->mapDistricts[$districtName];
            }

            // map account
            if($isMamnon != null){
                $isMamnon = 1;
            } else {
                $isMamnon = 0;
            }

            if($isTieuhoc != null){
                $isTieuhoc = 1;
            } else {
                $isMamnon = 0;
            }

            // map account type
            $mamnonAccountType = null;
            $tieuhocAccountType = null;
            if($isMamnon == 1){
                $mamnonAccountType = AccountType::query()->where('code', $values[10])->first();
                if($mamnonAccountType == null){
                    $this->count++;
                    $this->logicErrors = "Mã loại tài khoản mầm non không đúng ở dòng " . $this->count;
                    DB::rollBack();
                    return;
                }
            }

            if($isTieuhoc == 1){
                $tieuhocAccountType = AccountType::query()->where('code', $values[11])->first();
                if($tieuhocAccountType == null){
                    $this->count++;
                    $this->logicErrors = "Mã loại tài khoản tiểu học không đúng ở dòng " . $this->count;
                    DB::rollBack();
                    return;
                }
            }

            // time expired
            $dateExpiredExcel = $values[12];
            if($dateExpiredExcel != null){
                try {
                    $dateExpired = DateTime::createFromFormat(Constants::FORMAT_DATE_USER, $dateExpiredExcel);
                    if(!$dateExpired){
                        $this->count++;
                        $this->logicErrors = "Định dạng ngày hết hạn không đúng ở dòng " . $this->count;
                        DB::rollBack();
                        return;
                    }
                } catch (\Exception $e){
                    $this->count++;
                    $this->logicErrors = "Định dạng ngày hết hạn không đúng ở dòng " . $this->count;
                    DB::rollBack();
                    return;
                }
            }

            // --- SCHOOL CREATION/ASSOCIATION LOGIC ---
            $school = null;
            if ($nameOrg) {
                $school = \App\Models\School::where('name', $nameOrg)->first();
                if (!$school) {
                    $school = \App\Models\School::create([
                        'name' => $nameOrg,
                        'province' => $province != null ? $province['code'] : null,
                        'district' => $district != null ? $district['code'] : null,
                        'is_mamnon' => $isMamnon,
                        'is_tieuhoc' => $isTieuhoc,
                        'mamnon_account_type_id' => $mamnonAccountType != null ? $mamnonAccountType->id : null,
                        'tieuhoc_account_type_id' => $tieuhocAccountType != null ? $tieuhocAccountType->id : null,
                        'time_expired' => $dateExpired,
                    ]);
                }
            }

            $account = new Account();
            $account->fill([
                'name' => $name,
                'name_org' => $nameOrg,
                'email' => $email,
                'phone_number' => $phoneNumber,
                'password' => Hash::make($phoneNumber),
                'status' => $status,
                'class' => $class,
                'province' => $province != null? $province['code']: null,
                'district' => $district != null? $district['code']: null,
                'is_mamnon' => $isMamnon,
                'is_tieuhoc' => $isTieuhoc,
                'mamnon_account_type_id' => $mamnonAccountType != null? $mamnonAccountType->id : null,
                'tieuhoc_account_type_id' => $tieuhocAccountType != null? $tieuhocAccountType->id : null,
                'time_expired' => $dateExpired,
                'school_id' => $school ? $school->id : null,
            ]);
            $account->save();
            $this->count++;
        }

        DB::commit();
    }

    public function getLogicErrors()
    {
        return $this->logicErrors;
    }

    public function importedCount()
    {
        return $this->count;
    }

    public function totalCount()
    {
        return $this->totalCount;
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
